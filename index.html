<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Naroop - Connect & Share</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f8f6f3 0%, #ffffff 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 100vw;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 20px 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 32px;
            font-weight: 700;
            background: linear-gradient(45deg, #8B4513, #D2691E);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-buttons {
            display: flex;
            gap: 15px;
        }

        .nav-btn {
            background: transparent;
            border: 2px solid #8B4513;
            color: #8B4513;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: #8B4513;
            color: white;
            transform: translateY(-2px);
        }

        .nav-btn.primary {
            background: #8B4513;
            color: white;
        }

        .nav-btn.primary:hover {
            background: #7a3c0f;
        }

        /* Main Content */
        .main-content {
            display: grid;
            grid-template-columns: 1fr 2fr 1fr;
            gap: 24px;
            align-items: start;
            min-height: calc(100vh - 140px);
            padding: 0 20px;
        }

        .sidebar {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .sidebar h3 {
            margin-bottom: 20px;
            color: #333;
            font-size: 18px;
        }

        .sidebar-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 10px;
            padding: 12px 15px;
            margin-bottom: 8px;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
        }

        .sidebar-item:hover {
            background: rgba(139, 69, 19, 0.1);
            transform: translateX(5px);
        }

        .sidebar-item.active {
            background: rgba(139, 69, 19, 0.15);
            color: #8B4513;
            font-weight: 600;
        }

        .sidebar-item::before {
            content: "📱";
            margin-right: 12px;
            font-size: 16px;
        }

        .sidebar-item:nth-child(2)::before { content: "🏠"; }
        .sidebar-item:nth-child(3)::before { content: "🔍"; }
        .sidebar-item:nth-child(4)::before { content: "💬"; }
        .sidebar-item:nth-child(5)::before { content: "👤"; }

        /* Feed Section */
        .feed-section {
            background: #fafafa;
            border-radius: 0;
            padding: 0;
            box-shadow: none;
            max-width: 600px;
            margin: 0 auto;
        }

        .feed-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.06);
        }

        .feed-title {
            font-size: 28px;
            font-weight: 800;
            color: #1a1a1a;
            background: linear-gradient(135deg, #8B4513, #D2691E);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .refresh-btn {
            background: white;
            color: #6b7280;
            border: 1px solid rgba(0, 0, 0, 0.1);
            padding: 10px 16px;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .refresh-btn:hover {
            background: #f9fafb;
            color: #374151;
            border-color: rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .refresh-btn::before {
            content: '🔄';
            font-size: 14px;
        }

        .create-post {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .create-post::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #8B4513, #D2691E, #c9a876);
        }

        .create-post:hover {
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        }

        .create-post-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 20px;
        }

        .create-post-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(135deg, #8B4513, #D2691E);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            box-shadow: 0 4px 12px rgba(139, 69, 19, 0.3);
        }

        .create-post-content {
            flex: 1;
        }

        .create-post h3 {
            color: #1a1a1a;
            margin-bottom: 6px;
            font-size: 18px;
            font-weight: 700;
        }

        .create-post p {
            color: #6b7280;
            margin-bottom: 0;
            font-size: 14px;
            line-height: 1.5;
        }

        .create-btn {
            background: linear-gradient(135deg, #8B4513, #D2691E);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(139, 69, 19, 0.3);
            width: 100%;
            margin-top: 16px;
        }

        .create-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(139, 69, 19, 0.4);
            background: linear-gradient(135deg, #7a3c0f, #c55a11);
        }

        .create-btn:active {
            transform: translateY(0);
        }

        .load-more {
            background: white;
            border: 1px solid rgba(0, 0, 0, 0.1);
            color: #6b7280;
            padding: 16px 32px;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 24px;
            width: 100%;
            font-size: 14px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .load-more:hover {
            background: #f9fafb;
            color: #374151;
            border-color: rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* Post Cards */
        .post-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .post-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        }

        .post-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .post-author {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .author-avatar {
            width: 44px;
            height: 44px;
            border-radius: 50%;
            background: linear-gradient(135deg, #8B4513, #D2691E);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 2px 8px rgba(139, 69, 19, 0.3);
        }

        .author-info {
            flex: 1;
        }

        .author-name {
            font-weight: 600;
            color: #1a1a1a;
            font-size: 15px;
            margin-bottom: 2px;
        }

        .post-time {
            color: #6b7280;
            font-size: 13px;
        }

        .post-menu {
            position: relative;
        }

        .post-menu-btn {
            background: none;
            border: none;
            color: #9ca3af;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.2s ease;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .post-menu-btn:hover {
            background: rgba(0, 0, 0, 0.05);
            color: #6b7280;
        }

        .post-content {
            margin-bottom: 20px;
        }

        .post-title {
            font-size: 18px;
            font-weight: 700;
            color: #1a1a1a;
            margin-bottom: 12px;
            line-height: 1.4;
        }

        .post-text {
            color: #374151;
            line-height: 1.6;
            font-size: 15px;
            margin-bottom: 16px;
        }

        .post-image {
            width: 100%;
            border-radius: 12px;
            margin-top: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .post-actions {
            display: flex;
            align-items: center;
            gap: 4px;
            padding-top: 16px;
            border-top: 1px solid rgba(0, 0, 0, 0.06);
        }

        .action-btn {
            background: none;
            border: none;
            padding: 10px 16px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 500;
            color: #6b7280;
            flex: 1;
            justify-content: center;
        }

        .action-btn:hover {
            background: rgba(0, 0, 0, 0.04);
            color: #374151;
        }

        .action-btn.liked {
            color: #ef4444;
        }

        .action-btn.liked:hover {
            background: rgba(239, 68, 68, 0.1);
        }

        .action-icon {
            font-size: 16px;
            filter: grayscale(1);
            transition: filter 0.2s ease;
        }

        .action-btn:hover .action-icon,
        .action-btn.liked .action-icon {
            filter: grayscale(0);
        }

        .action-text {
            font-weight: 600;
        }

        .post-stats {
            margin-top: 12px;
            padding-top: 12px;
            border-top: 1px solid rgba(0, 0, 0, 0.06);
            color: #6b7280;
            font-size: 13px;
            text-align: center;
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6b7280;
        }

        .empty-state-icon {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #374151;
        }

        .empty-state p {
            font-size: 15px;
            max-width: 400px;
            margin: 0 auto;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .post-card {
            animation: fadeInUp 0.5s ease-out;
        }

        .create-post {
            animation: fadeInUp 0.3s ease-out;
        }

        .action-btn:active {
            animation: pulse 0.2s ease-out;
        }

        /* Content Sections */
        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
        }

        /* Explore Section Styles */
        .explore-content {
            text-align: center;
            padding: 40px 20px;
        }

        .explore-content h3 {
            font-size: 24px;
            color: #8B4513;
            margin-bottom: 15px;
        }

        .explore-content p {
            color: #666;
            font-size: 16px;
            margin-bottom: 30px;
        }

        .explore-categories {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
        }

        .category-tag {
            background: linear-gradient(45deg, #8B4513, #D2691E);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .category-tag:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
        }

        /* Messages Section Styles */
        .messages-content {
            text-align: center;
            padding: 40px 20px;
        }

        .messages-content h3 {
            font-size: 24px;
            color: #8B4513;
            margin-bottom: 15px;
        }

        .messages-content p {
            color: #666;
            font-size: 16px;
            margin-bottom: 30px;
        }

        .messages-placeholder {
            background: rgba(201, 168, 118, 0.1);
            border-radius: 15px;
            padding: 30px;
            color: #999;
        }

        /* Profile Section Styles */
        .profile-content {
            padding: 20px 0;
        }

        .profile-header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .profile-avatar {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #8B4513, #D2691E);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            color: white;
            margin-right: 20px;
        }

        .profile-info h3 {
            font-size: 24px;
            color: #333;
            margin-bottom: 5px;
        }

        .profile-info p {
            color: #666;
            font-size: 16px;
        }

        .profile-stats {
            display: flex;
            gap: 40px;
            margin-bottom: 30px;
            justify-content: center;
        }

        .stat {
            text-align: center;
        }

        .stat-number {
            display: block;
            font-size: 24px;
            font-weight: 700;
            color: #8B4513;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
        }

        .profile-actions {
            text-align: center;
        }

        /* Right Sidebar */
        .trending {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .trending-item {
            padding: 15px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .trending-item:hover {
            background: rgba(201, 168, 118, 0.05);
            border-radius: 10px;
            padding: 15px;
            margin: 0 -15px;
        }

        .trending-item:last-child {
            border-bottom: none;
        }

        .trending-topic {
            font-weight: 600;
            color: #c9a876;
            margin-bottom: 5px;
        }

        .trending-count {
            font-size: 12px;
            color: #999;
        }

        /* Mobile Bottom Navigation */
        .mobile-nav {
            display: none;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 12px 0;
            border-top: 1px solid rgba(139, 69, 19, 0.1);
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        .mobile-nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
            max-width: 600px;
            margin: 0 auto;
            padding: 0 10px;
        }

        .mobile-nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 6px 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 12px;
            min-width: 50px;
            flex: 1;
            max-width: 80px;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
            touch-action: manipulation;
        }

        .mobile-nav-item:hover,
        .mobile-nav-item.active {
            background: rgba(139, 69, 19, 0.1);
            color: #8B4513;
            transform: translateY(-2px);
        }

        .mobile-nav-item .icon {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .mobile-nav-item .label {
            font-size: 11px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">Naroop</div>
            <div class="nav-buttons"></div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Left Sidebar -->
            <aside class="sidebar">
                <h3>Navigation</h3>
                <div class="sidebar-item active" data-section="feed">Feed</div>
                <div class="sidebar-item" data-section="explore">Explore</div>
                <div class="sidebar-item" data-section="messages">Messages</div>
                <div class="sidebar-item" data-section="profile">Profile</div>
            </aside>

            <!-- Feed Section -->
            <section class="feed-section content-section active" id="feed-section">
                <div class="feed-header">
                    <h2 class="feed-title">Your Feed</h2>
                    <button class="refresh-btn">Refresh</button>
                </div>

                <div class="create-post">
                    <div class="create-post-header">
                        <div class="create-post-icon">✨</div>
                        <div class="create-post-content">
                            <h3>Share Your Story</h3>
                            <p>What positive experience would you like to share with the community today?</p>
                        </div>
                    </div>
                    <button class="create-btn" id="createPostBtn">Create Post</button>
                </div>

                <div id="postsContainer">
                    <!-- Posts will be loaded here -->
                </div>

                <button class="load-more">Load More Posts</button>
            </section>

            <!-- Explore Section -->
            <section class="content-section" id="explore-section">
                <div class="feed-header">
                    <h2 class="feed-title">Explore</h2>
                    <button class="refresh-btn">Refresh</button>
                </div>
                <div class="explore-content">
                    <h3>🔍 Discover New Content</h3>
                    <p>Explore trending posts and discover new voices in the community.</p>
                    <div class="explore-categories">
                        <div class="category-tag">#Trending</div>
                        <div class="category-tag">#BlackExcellence</div>
                        <div class="category-tag">#CommunityLove</div>
                        <div class="category-tag">#Inspiration</div>
                    </div>
                </div>
            </section>

            <!-- Messages Section -->
            <section class="content-section" id="messages-section">
                <div class="feed-header">
                    <h2 class="feed-title">Messages</h2>
                    <button class="refresh-btn">Refresh</button>
                </div>
                <div class="messages-content">
                    <h3>💬 Your Messages</h3>
                    <p>Connect and communicate with your community.</p>
                    <div class="messages-placeholder">
                        <p>No messages yet. Start a conversation!</p>
                    </div>
                </div>
            </section>

            <!-- Profile Section -->
            <section class="content-section" id="profile-section">
                <div class="feed-header">
                    <h2 class="feed-title">Profile</h2>
                    <button class="refresh-btn">Edit Profile</button>
                </div>
                <div class="profile-content">
                    <div class="profile-header">
                        <div class="profile-avatar">👤</div>
                        <div class="profile-info">
                            <h3 id="profileUsername">Loading...</h3>
                            <p id="profileEmail">Loading...</p>
                        </div>
                    </div>
                    <div class="profile-stats">
                        <div class="stat">
                            <span class="stat-number">0</span>
                            <span class="stat-label">Posts</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">0</span>
                            <span class="stat-label">Followers</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">0</span>
                            <span class="stat-label">Following</span>
                        </div>
                    </div>
                    <div class="profile-actions">
                        <button class="nav-btn" id="signOutBtn">Sign Out</button>
                    </div>
                </div>
            </section>

            <!-- Right Sidebar -->
            <aside class="trending">
                <h3>Trending Topics</h3>
                <div class="trending-item">
                    <div class="trending-topic">#BlackExcellence</div>
                    <div class="trending-count">2.1M posts</div>
                </div>
                <div class="trending-item">
                    <div class="trending-topic">#CommunityLove</div>
                    <div class="trending-count">980K posts</div>
                </div>
                <div class="trending-item">
                    <div class="trending-topic">#Inspiration</div>
                    <div class="trending-count">1.5M posts</div>
                </div>
                <div class="trending-item">
                    <div class="trending-topic">#BlackJoy</div>
                    <div class="trending-count">750K posts</div>
                </div>
                <div class="trending-item">
                    <div class="trending-topic">#Success</div>
                    <div class="trending-count">1.2M posts</div>
                </div>
            </aside>
        </main>

        <!-- Mobile Navigation -->
        <nav class="mobile-nav">
            <div class="mobile-nav-items">
                <div class="mobile-nav-item active" data-section="feed">
                    <div class="icon">🏠</div>
                    <div class="label">Home</div>
                </div>
                <div class="mobile-nav-item" data-section="explore">
                    <div class="icon">🔍</div>
                    <div class="label">Explore</div>
                </div>
                <div class="mobile-nav-item" data-section="messages">
                    <div class="icon">💬</div>
                    <div class="label">Messages</div>
                </div>
                <div class="mobile-nav-item" data-section="profile">
                    <div class="icon">👤</div>
                    <div class="label">Profile</div>
                </div>
            </div>
        </nav>
    </div>

    <!-- Mobile Responsive Styles -->
    <style>
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header {
                padding: 15px 20px;
                margin-bottom: 20px;
            }

            .logo {
                font-size: 24px;
            }

            .nav-buttons {
                gap: 10px;
            }

            .nav-btn {
                padding: 8px 16px;
                font-size: 14px;
            }

            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
                padding-bottom: 80px;
            }

            .sidebar,
            .trending {
                display: none;
            }

            .mobile-nav {
                display: block;
            }

            .feed-section {
                padding: 20px;
            }

            .feed-title {
                font-size: 20px;
            }

            .refresh-btn {
                padding: 10px 20px;
                font-size: 14px;
            }

            .create-post {
                padding: 20px;
            }

            .create-post-header {
                flex-direction: column;
                text-align: center;
                gap: 12px;
            }

            .create-post h3 {
                font-size: 16px;
            }

            .create-post p {
                font-size: 13px;
            }

            .create-btn {
                padding: 12px 24px;
                font-size: 14px;
            }

            .post-card {
                padding: 20px;
                margin-bottom: 16px;
            }

            .post-title {
                font-size: 16px;
            }

            .post-text {
                font-size: 14px;
            }

            .action-btn {
                padding: 8px 12px;
                font-size: 13px;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 5px;
            }

            .header {
                padding: 12px 15px;
                border-radius: 15px;
            }

            .logo {
                font-size: 20px;
            }

            .nav-btn {
                padding: 6px 12px;
                font-size: 12px;
            }

            .feed-section {
                padding: 15px;
                border-radius: 15px;
            }

            .create-post {
                padding: 16px;
                border-radius: 12px;
            }

            .create-post-icon {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .create-post h3 {
                font-size: 15px;
            }

            .post-card {
                padding: 16px;
                border-radius: 12px;
            }

            .author-avatar {
                width: 36px;
                height: 36px;
                font-size: 14px;
            }

            .post-title {
                font-size: 15px;
            }

            .action-btn {
                padding: 6px 8px;
                font-size: 12px;
            }

            .mobile-nav-item .icon {
                font-size: 18px;
            }

            .mobile-nav-item .label {
                font-size: 10px;
            }
        }

        /* Create Post Modal Styles */
        .post-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(8px);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .post-modal-content {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .post-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 25px 30px 20px;
            border-bottom: 1px solid #f0f0f0;
        }

        .post-modal-header h3 {
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin: 0;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 28px;
            color: #999;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .close-btn:hover {
            background: #f5f5f5;
            color: #666;
        }

        .post-form {
            padding: 30px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            font-size: 16px;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 15px 18px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            font-family: inherit;
            transition: all 0.3s ease;
            background: #fafbfc;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #c9a876;
            background: white;
            box-shadow: 0 0 0 3px rgba(201, 168, 118, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 120px;
            line-height: 1.5;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #f0f0f0;
        }

        .cancel-btn,
        .submit-btn {
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            min-width: 100px;
        }

        .cancel-btn {
            background: #f8f9fa;
            color: #666;
            border: 2px solid #e1e5e9;
        }

        .cancel-btn:hover {
            background: #e9ecef;
            color: #495057;
        }

        .submit-btn {
            background: linear-gradient(45deg, #c9a876, #d4b896);
            color: white;
            box-shadow: 0 4px 15px rgba(201, 168, 118, 0.3);
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(201, 168, 118, 0.4);
        }

        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .post-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 20px;
            color: #666;
            font-weight: 500;
        }

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #c9a876;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Mobile responsive styles for modal */
        @media (max-width: 768px) {
            .post-modal {
                padding: 10px;
            }

            .post-modal-content {
                border-radius: 15px;
                max-height: 95vh;
            }

            .post-modal-header {
                padding: 20px 20px 15px;
            }

            .post-modal-header h3 {
                font-size: 20px;
            }

            .post-form {
                padding: 20px;
            }

            .form-group {
                margin-bottom: 20px;
            }

            .form-actions {
                flex-direction: column-reverse;
                gap: 10px;
            }

            .cancel-btn,
            .submit-btn {
                width: 100%;
                padding: 15px;
            }
        }
    </style>

    <!-- JavaScript -->
    <script>
        // Authentication functions (placeholders)
        function showSignIn() {
            // Always clear any stored user/auth data and sign out from Firebase before showing sign-in UI
            console.log('🔐 Sign In button clicked - clearing auth state first...');

            if (window.FirebaseAuth && typeof window.FirebaseAuth.signOut === 'function') {
                window.FirebaseAuth.signOut().then((result) => {
                    console.log('Firebase sign out result:', result);

                    // Clear all local storage
                    if (window.CoreUtils && typeof window.CoreUtils.clearUserSession === 'function') {
                        window.CoreUtils.clearUserSession();
                    } else {
                        // Fallback manual clearing
                        localStorage.removeItem('currentUser');
                        localStorage.removeItem('authToken');
                        localStorage.removeItem('sessionData');
                        sessionStorage.clear();
                    }

                    // Clear app state
                    if (window.AppState) {
                        window.AppState.currentUser = null;
                        window.AppState.isAuthenticated = false;
                    }

                    window.dispatchEvent(new Event('authChanged'));

                    // Redirect to landing page for proper sign-in flow
                    console.log('🔄 Redirecting to landing page for sign-in...');
                    window.location.href = '/landing.html';
                }).catch(error => {
                    console.error('Error during sign out before sign in:', error);
                    // Force clear and redirect anyway
                    if (window.CoreUtils && typeof window.CoreUtils.clearUserSession === 'function') {
                        window.CoreUtils.clearUserSession();
                    }
                    window.location.href = '/landing.html';
                });
            } else {
                // Firebase not available, clear manually and redirect
                if (window.CoreUtils && typeof window.CoreUtils.clearUserSession === 'function') {
                    window.CoreUtils.clearUserSession();
                } else {
                    localStorage.removeItem('currentUser');
                    localStorage.removeItem('authToken');
                    localStorage.removeItem('sessionData');
                    sessionStorage.clear();
                    if (window.AppState) {
                        window.AppState.currentUser = null;
                        window.AppState.isAuthenticated = false;
                    }
                    window.dispatchEvent(new Event('authChanged'));
                }
                window.location.href = '/landing.html';
            }
        }

        function showSignUp() {
            alert('Sign Up functionality will be implemented with Firebase Authentication');
        }

        // Navigation functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Desktop sidebar navigation
            const sidebarItems = document.querySelectorAll('.sidebar-item');
            const mobileNavItems = document.querySelectorAll('.mobile-nav-item');

            function setActiveNavigation(section) {
                // Update desktop sidebar
                sidebarItems.forEach(item => {
                    item.classList.remove('active');
                    if (item.dataset.section === section) {
                        item.classList.add('active');
                    }
                });

                // Update mobile navigation
                mobileNavItems.forEach(item => {
                    item.classList.remove('active');
                    if (item.dataset.section === section) {
                        item.classList.add('active');
                    }
                });

                // Here you would typically show/hide different content sections
                console.log('Navigating to:', section);
            }

            // Add click handlers for desktop sidebar
            sidebarItems.forEach(item => {
                item.addEventListener('click', function() {
                    const section = this.dataset.section;
                    setActiveNavigation(section);
                });
            });

            // Add click handlers for mobile navigation
            mobileNavItems.forEach(item => {
                item.addEventListener('click', function() {
                    const section = this.dataset.section;
                    setActiveNavigation(section);
                });
            });

            // Refresh button functionality
            const refreshBtn = document.querySelector('.refresh-btn');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', function() {
                    console.log('Refreshing feed...');
                    // Add refresh animation
                    this.style.transform = 'rotate(360deg)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 500);
                });
            }

            // Create post button functionality is now handled by Posts.js

            // Load more button functionality
            const loadMoreBtn = document.querySelector('.load-more');
            if (loadMoreBtn) {
                loadMoreBtn.addEventListener('click', function() {
                    console.log('Loading more posts...');
                    this.textContent = 'Loading...';
                    setTimeout(() => {
                        this.textContent = 'Load More Posts';
                    }, 1000);
                });
            }

            // Trending items functionality
            const trendingItems = document.querySelectorAll('.trending-item');
            trendingItems.forEach(item => {
                item.addEventListener('click', function() {
                    const topic = this.querySelector('.trending-topic').textContent;
                    console.log('Clicked trending topic:', topic);
                });
            });
        });

        // Add smooth scrolling for better UX
        document.documentElement.style.scrollBehavior = 'smooth';

        // Add loading state management
        window.addEventListener('load', function() {
            document.body.style.opacity = '1';
            document.body.style.transition = 'opacity 0.3s ease-in-out';
        });

        // Add touch feedback for mobile
        if ('ontouchstart' in window) {
            document.addEventListener('touchstart', function() {}, {passive: true});
        }
    </script>

    <!-- Create Post Modal -->
    <div id="postModal" class="post-modal" style="display: none;">
        <div class="post-modal-content">
            <div class="post-modal-header">
                <h3>Create New Post</h3>
                <button class="close-btn" onclick="Posts.closeCreatePostModal()">&times;</button>
            </div>
            <form id="postForm" class="post-form">
                <div class="form-group">
                    <label for="postTitle">Title</label>
                    <input type="text" id="postTitle" placeholder="What's your story about?" required>
                </div>
                <div class="form-group">
                    <label for="postContent">Content</label>
                    <textarea id="postContent" placeholder="Share your positive experience..." rows="6" required></textarea>
                </div>
                <div id="postLoading" class="post-loading" style="display: none;">
                    <div class="loading-spinner"></div>
                    <span>Creating your story...</span>
                </div>
                <div class="form-actions">
                    <button type="button" class="cancel-btn" onclick="Posts.closeCreatePostModal()">Cancel</button>
                    <button type="submit" class="submit-btn">Share Story</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Include external JavaScript files -->
    <script type="module" src="./public/js/firebase-config.js"></script>
    <script type="module" src="./public/js/authentication.js"></script>
    <script type="module" src="./public/js/core.js"></script>
    <script type="module" src="./public/js/navigation.js"></script>
    <script type="module" src="./public/js/posts.js"></script>
    <script type="module" src="./public/js/profile.js"></script>

    <!-- Additional functionality for navigation sections -->
    <script type="module">
        import { Authentication } from './public/js/authentication.js';
        import { FirebaseAuth } from './public/js/firebase-config.js';
        import { AppState } from './public/js/core.js';

        // Initialize profile section when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Load user profile data
            loadProfileData();

            // Setup sign out button
            const signOutBtn = document.getElementById('signOutBtn');
            if (signOutBtn) {
                signOutBtn.addEventListener('click', handleSignOut);
            }
        });

        // Load user profile data
        function loadProfileData() {
            try {
                const currentUser = localStorage.getItem('currentUser');
                if (currentUser) {
                    const userData = JSON.parse(currentUser);
                    
                    // Update profile UI
                    const profileUsername = document.getElementById('profileUsername');
                    const profileEmail = document.getElementById('profileEmail');
                    
                    if (profileUsername) {
                        profileUsername.textContent = userData.username || userData.displayName || 'User';
                    }
                    
                    if (profileEmail) {
                        profileEmail.textContent = userData.email || 'No email provided';
                    }
                }
            } catch (error) {
                console.error('Error loading profile data:', error);
            }
        }

        // Handle sign out
        async function handleSignOut() {
            try {
                const confirmSignOut = confirm('Are you sure you want to sign out?');
                if (!confirmSignOut) return;

                // Show loading state
                const signOutBtn = document.getElementById('signOutBtn');
                const originalText = signOutBtn.textContent;
                signOutBtn.textContent = 'Signing out...';
                signOutBtn.disabled = true;

                // Sign out using Firebase Auth
                const result = await FirebaseAuth.signOut();
                
                if (result.success) {
                    // Clear local storage
                    localStorage.removeItem('currentUser');
                    localStorage.removeItem('authToken');
                    
                    // Redirect to landing page
                    window.location.href = '/landing.html';
                } else {
                    alert('Error signing out. Please try again.');
                    signOutBtn.textContent = originalText;
                    signOutBtn.disabled = false;
                }
            } catch (error) {
                console.error('Sign out error:', error);
                alert('Error signing out. Please try again.');
                
                // Reset button
                const signOutBtn = document.getElementById('signOutBtn');
                signOutBtn.textContent = 'Sign Out';
                signOutBtn.disabled = false;
            }
        }

        // Update profile data when navigation changes to profile section
        window.addEventListener('hashchange', function() {
            if (window.location.hash === '#profile') {
                loadProfileData();
            }
        });
    </script>

    <script src="public/js/core.js"></script>
    <script src="public/js/header.js"></script>
    <script src="public/js/authentication.js"></script>
</body>
</html>
