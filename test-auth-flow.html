<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Flow Test - Naroop</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-step {
            margin: 10px 0;
            padding: 10px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .storage-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔐 Naroop Authentication Flow Test</h1>
    
    <div class="test-container">
        <h2>Test Instructions</h2>
        <div class="test-step">
            <strong>Step 1:</strong> Check current authentication state
        </div>
        <div class="test-step">
            <strong>Step 2:</strong> Clear all authentication data
        </div>
        <div class="test-step">
            <strong>Step 3:</strong> Go to landing page and test sign-in flow
        </div>
        <div class="test-step">
            <strong>Step 4:</strong> After signing in, test logout
        </div>
        <div class="test-step">
            <strong>Step 5:</strong> Verify that sign-in requires credentials again
        </div>
    </div>

    <div class="test-container">
        <h2>Current Authentication State</h2>
        <div id="authState" class="test-result info">Checking...</div>
        <div id="storageInfo" class="storage-info">Loading storage info...</div>
        
        <button onclick="checkAuthState()">🔍 Check Auth State</button>
        <button onclick="clearAllAuth()">🧹 Clear All Auth Data</button>
        <button onclick="goToLanding()">🏠 Go to Landing Page</button>
        <button onclick="goToMain()">📱 Go to Main App</button>
    </div>

    <div class="test-container">
        <h2>Test Results</h2>
        <div id="testResults"></div>
    </div>

    <script type="module">
        import { FirebaseAuth } from './public/js/firebase-config.js';
        
        let firebaseInitialized = false;
        
        // Initialize Firebase
        async function initFirebase() {
            try {
                firebaseInitialized = await FirebaseAuth.init();
                addTestResult(firebaseInitialized ? 'Firebase initialized successfully' : 'Firebase initialization failed', firebaseInitialized ? 'success' : 'error');
                return firebaseInitialized;
            } catch (error) {
                addTestResult(`Firebase initialization error: ${error.message}`, 'error');
                return false;
            }
        }
        
        // Check authentication state
        window.checkAuthState = async function() {
            const authStateDiv = document.getElementById('authState');
            const storageDiv = document.getElementById('storageInfo');
            
            try {
                // Check local storage
                const currentUser = localStorage.getItem('currentUser');
                const authToken = localStorage.getItem('authToken');
                const sessionData = localStorage.getItem('sessionData');
                
                // Check Firebase auth state
                let firebaseUser = null;
                if (firebaseInitialized) {
                    firebaseUser = FirebaseAuth.getCurrentUser();
                }
                
                // Display results
                const hasLocalAuth = !!(currentUser && authToken);
                const hasFirebaseAuth = !!firebaseUser;
                
                let stateText = `Local Auth: ${hasLocalAuth ? '✅ Yes' : '❌ No'}\n`;
                stateText += `Firebase Auth: ${hasFirebaseAuth ? '✅ Yes' : '❌ No'}`;
                
                authStateDiv.textContent = stateText;
                authStateDiv.className = `test-result ${(hasLocalAuth || hasFirebaseAuth) ? 'success' : 'info'}`;
                
                // Display storage info
                let storageText = 'Local Storage:\n';
                storageText += `currentUser: ${currentUser ? 'Present' : 'None'}\n`;
                storageText += `authToken: ${authToken ? 'Present' : 'None'}\n`;
                storageText += `sessionData: ${sessionData ? 'Present' : 'None'}\n`;
                
                if (firebaseUser) {
                    storageText += `\nFirebase User:\n`;
                    storageText += `UID: ${firebaseUser.uid}\n`;
                    storageText += `Email: ${firebaseUser.email}`;
                }
                
                storageDiv.textContent = storageText;
                
                addTestResult(`Auth state checked - Local: ${hasLocalAuth}, Firebase: ${hasFirebaseAuth}`, 'info');
                
            } catch (error) {
                authStateDiv.textContent = `Error checking auth state: ${error.message}`;
                authStateDiv.className = 'test-result error';
                addTestResult(`Error checking auth state: ${error.message}`, 'error');
            }
        };
        
        // Clear all authentication data
        window.clearAllAuth = async function() {
            try {
                addTestResult('Starting complete auth data clearing...', 'info');
                
                // Clear Firebase auth
                if (firebaseInitialized) {
                    const result = await FirebaseAuth.signOut();
                    addTestResult(`Firebase sign out: ${result.success ? 'Success' : 'Failed - ' + result.error}`, result.success ? 'success' : 'error');
                }
                
                // Clear local storage
                localStorage.clear();
                sessionStorage.clear();
                
                // Clear IndexedDB (Firebase storage)
                if ('indexedDB' in window) {
                    const firebaseDBNames = [
                        'firebaseLocalStorageDb',
                        'firebase-heartbeat-database',
                        'firebase-installations-database'
                    ];
                    
                    for (const dbName of firebaseDBNames) {
                        try {
                            await new Promise((resolve, reject) => {
                                const deleteReq = indexedDB.deleteDatabase(dbName);
                                deleteReq.onsuccess = () => resolve();
                                deleteReq.onerror = () => reject(deleteReq.error);
                                deleteReq.onblocked = () => reject(new Error('Database deletion blocked'));
                                setTimeout(() => reject(new Error('Timeout')), 5000);
                            });
                            addTestResult(`Cleared IndexedDB: ${dbName}`, 'success');
                        } catch (error) {
                            addTestResult(`Could not clear IndexedDB ${dbName}: ${error.message}`, 'error');
                        }
                    }
                }
                
                addTestResult('All authentication data cleared successfully', 'success');
                
                // Refresh auth state display
                setTimeout(checkAuthState, 500);
                
            } catch (error) {
                addTestResult(`Error clearing auth data: ${error.message}`, 'error');
            }
        };
        
        // Navigation functions
        window.goToLanding = function() {
            window.location.href = '/landing.html';
        };
        
        window.goToMain = function() {
            window.location.href = '/index.html';
        };
        
        // Add test result
        function addTestResult(message, type) {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', async function() {
            await initFirebase();
            checkAuthState();
        });
    </script>
</body>
</html>
