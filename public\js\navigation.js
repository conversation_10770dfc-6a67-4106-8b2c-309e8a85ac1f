// Navigation system for Naroop
import { CoreUtils } from './core.js';

console.log('🧭 Loading navigation system...');

export const Navigation = {
    currentSection: 'feed',
    
    // Initialize navigation system
    init() {
        console.log('🔧 Initializing navigation...');
        this.setupNavigationListeners();
        this.initializeWithCorrectSection();
        console.log('✅ Navigation initialized successfully');
    },

    // Switch to a specific section
    switchToSection(sectionName) {
        console.log('🔄 Switching to section:', sectionName);

        // Hide all sections
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });

        // Show target section
        const targetSection = document.getElementById(`${sectionName}-section`);
        if (targetSection) {
            targetSection.classList.add('active');
            this.currentSection = sectionName;
            console.log('✅ Switched to:', sectionName);
        } else {
            console.error('❌ Section not found:', sectionName);
            return false;
        }

        // Update navigation active states
        this.updateNavigationStates(sectionName);

        // Update URL hash
        if (window.location.hash !== `#${sectionName}`) {
            window.history.pushState(null, null, `#${sectionName}`);
        }

        // Handle section-specific initialization
        this.handleSectionChange(sectionName);

        return true;
    },

    // Update navigation button states
    updateNavigationStates(activeSectionName) {
        // Update sidebar navigation
        document.querySelectorAll('.sidebar-item').forEach(item => {
            item.classList.remove('active');
            if (item.dataset.section === activeSectionName) {
                item.classList.add('active');
            }
        });

        // Update mobile navigation
        document.querySelectorAll('.mobile-nav-item').forEach(item => {
            item.classList.remove('active');
            if (item.dataset.section === activeSectionName) {
                item.classList.add('active');
            }
        });
    },

    // Setup navigation event listeners
    setupNavigationListeners() {
        // Sidebar navigation
        document.querySelectorAll('.sidebar-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                const sectionName = item.dataset.section;
                if (sectionName) {
                    this.switchToSection(sectionName);
                }
            });
        });

        // Mobile navigation
        document.querySelectorAll('.mobile-nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                const sectionName = item.dataset.section;
                if (sectionName) {
                    this.switchToSection(sectionName);
                }
            });
        });

        // Handle browser back/forward navigation
        window.addEventListener('popstate', () => {
            this.handleInitialHash();
        });
    },

    // Initialize with correct section based on URL hash
    initializeWithCorrectSection() {
        this.handleInitialHash();
    },

    // Handle initial hash or default to feed
    handleInitialHash() {
        const hash = window.location.hash.substring(1);
        const initialSection = (hash && document.getElementById(`${hash}-section`)) ? hash : 'feed';
        this.switchToSection(initialSection);
    },

    // Handle section-specific initialization
    handleSectionChange(sectionName) {
        switch (sectionName) {
            case 'feed':
                // Initialize feed if needed
                if (window.Posts && typeof window.Posts.loadPosts === 'function') {
                    window.Posts.loadPosts();
                }
                break;
            case 'profile':
                // Initialize profile if needed
                if (window.Profile && typeof window.Profile.loadUserProfile === 'function') {
                    window.Profile.loadUserProfile();
                }
                break;
            case 'explore':
                // Initialize explore section
                this.initializeExploreSection();
                break;
            case 'messages':
                // Initialize messages section
                this.initializeMessagesSection();
                break;
            case 'settings':
                // Initialize settings section
                this.initializeSettingsSection();
                break;
        }
    },

    // Initialize explore section
    initializeExploreSection() {
        console.log('🔍 Initializing explore section...');
        // Add explore-specific functionality here
    },

    // Initialize messages section
    initializeMessagesSection() {
        console.log('💬 Initializing messages section...');
        // Add messages-specific functionality here
    },

    // Initialize settings section
    initializeSettingsSection() {
        console.log('⚙️ Initializing settings section...');
        // Add settings-specific functionality here
    },

    // Get current section
    getCurrentSection() {
        return this.currentSection;
    },

    // Check if section exists
    sectionExists(sectionName) {
        return document.getElementById(`${sectionName}-section`) !== null;
    }
};

// Initialize navigation when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Wait a bit for other modules to load
    setTimeout(() => {
        Navigation.init();
    }, 100);
});

// Make navigation available globally
window.Navigation = Navigation;
