// Core functionality and global state management for Naroop
console.log('🚀 Loading core functionality...');

// Global state
export const AppState = {
    currentPosts: [],
    postsLoaded: 0,
    currentUser: null,
    isAuthenticated: false
};

// Core utility functions
export const CoreUtils = {
    // Initialize the application
    init() {
        console.log('🔧 Initializing core application...');
        this.loadStoredUser();
        this.setupGlobalEventListeners();
    },

    // Load user from localStorage
    loadStoredUser() {
        try {
            const storedUser = localStorage.getItem('currentUser');
            if (storedUser) {
                AppState.currentUser = JSON.parse(storedUser);
                AppState.isAuthenticated = true;
                console.log('✅ User loaded from storage:', AppState.currentUser.username);
                window.dispatchEvent(new Event('authChanged'));
            }
        } catch (error) {
            console.error('Error loading stored user:', error);
        }
    },

    // Setup global event listeners
    setupGlobalEventListeners() {
        // Handle browser navigation
        window.addEventListener('hashchange', () => {
            const hash = window.location.hash.substring(1);
            if (hash && document.getElementById(`${hash}-section`)) {
                window.Navigation?.switchToSection(hash);
            }
        });

        // Handle logout button
        const logoutBtn = document.querySelector('.nav-btn.primary');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', this.handleLogout.bind(this));
        }
    },

    // Handle logout
    handleLogout() {
        console.log('🚪 Logout clicked');
        if (confirm('Do you want to logout?')) {
            this.clearUserSession();
            window.location.href = '/landing.html';
        }
    },

    // Clear user session
    clearUserSession() {
        try {
            // Clear all authentication-related localStorage items
            localStorage.removeItem('currentUser');
            localStorage.removeItem('authToken');
            localStorage.removeItem('sessionData');

            // Clear sessionStorage completely
            sessionStorage.clear();

            // Clear any Firebase-related storage
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (key.startsWith('firebase:') || key.includes('firebaseLocalStorageDb'))) {
                    keysToRemove.push(key);
                }
            }

            keysToRemove.forEach(key => {
                try {
                    localStorage.removeItem(key);
                } catch (e) {
                    console.warn(`Could not clear key ${key}:`, e);
                }
            });

            // Reset app state
            AppState.currentUser = null;
            AppState.isAuthenticated = false;

            // Dispatch auth changed event
            window.dispatchEvent(new Event('authChanged'));

            console.log('✅ User session cleared completely');
        } catch (error) {
            console.error('Error clearing user session:', error);

            // Fallback: at least clear the essential items
            try {
                localStorage.removeItem('currentUser');
                localStorage.removeItem('authToken');
                localStorage.removeItem('sessionData');
                AppState.currentUser = null;
                AppState.isAuthenticated = false;
                window.dispatchEvent(new Event('authChanged'));
            } catch (fallbackError) {
                console.error('Critical error in session clearing fallback:', fallbackError);
            }
        }
    },

    // Show loading state
    showLoading(element) {
        if (element) {
            element.style.display = 'block';
        }
    },

    // Hide loading state
    hideLoading(element) {
        if (element) {
            element.style.display = 'none';
        }
    },

    // Show error message
    showError(message, container = null) {
        console.error('Error:', message);
        
        if (container) {
            container.innerHTML = `<div class="error-message">${message}</div>`;
            container.style.display = 'block';
        } else {
            // Fallback to alert for now (will be replaced with proper UI)
            alert(message);
        }
    },

    // Hide error message
    hideError(container) {
        if (container) {
            container.style.display = 'none';
        }
    },

    // Format time ago
    getTimeAgo(dateString) {
        const now = new Date();
        const postDate = new Date(dateString);
        const diffInSeconds = Math.floor((now - postDate) / 1000);

        if (diffInSeconds < 60) {
            return 'Just now';
        } else if (diffInSeconds < 3600) {
            const minutes = Math.floor(diffInSeconds / 60);
            return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
        } else if (diffInSeconds < 86400) {
            const hours = Math.floor(diffInSeconds / 3600);
            return `${hours} hour${hours > 1 ? 's' : ''} ago`;
        } else if (diffInSeconds < 604800) {
            const days = Math.floor(diffInSeconds / 86400);
            return `${days} day${days > 1 ? 's' : ''} ago`;
        } else {
            return postDate.toLocaleDateString();
        }
    },

    // Generate unique ID
    generateId() {
        return Date.now().toString() + Math.random().toString(36).substr(2, 9);
    },

    // Validate email format
    validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    // Sanitize HTML content
    sanitizeHtml(html) {
        const div = document.createElement('div');
        div.textContent = html;
        return div.innerHTML;
    },

    // Debounce function
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Throttle function
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
};

// Initialize core functionality when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    CoreUtils.init();
});

// Make core utilities available globally for backward compatibility
window.CoreUtils = CoreUtils;
window.AppState = AppState;
