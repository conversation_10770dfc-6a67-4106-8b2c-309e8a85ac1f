// Profile management system for Naroop
import { AppState, CoreUtils } from './core.js';
import { Authentication } from './authentication.js';

console.log('👤 Loading profile system...');

export const Profile = {
    profileUser: null,
    isEditing: false,

    // Initialize profile system
    init() {
        console.log('🔧 Initializing profile system...');
        this.setupEventListeners();
        console.log('✅ Profile system initialized');
    },

    // Setup event listeners
    setupEventListeners() {
        // Edit profile button
        const editProfileBtn = document.getElementById('editProfileBtn');
        if (editProfileBtn) {
            editProfileBtn.addEventListener('click', () => this.toggleEditMode());
        }

        // Save profile button
        const saveProfileBtn = document.getElementById('saveProfileBtn');
        if (saveProfileBtn) {
            saveProfileBtn.addEventListener('click', () => this.saveProfile());
        }

        // Cancel edit button
        const cancelEditBtn = document.getElementById('cancelEditBtn');
        if (cancelEditBtn) {
            cancelEditBtn.addEventListener('click', () => this.cancelEdit());
        }

        // Avatar input
        const avatarInput = document.getElementById('avatarInput');
        if (avatarInput) {
            avatarInput.addEventListener('change', (e) => this.handleAvatarChange(e));
        }
    },

    // Load user profile
    async loadUserProfile() {
        try {
            this.showProfileLoading();

            // Get current user from localStorage first
            const storedUser = localStorage.getItem('currentUser');
            if (storedUser) {
                this.profileUser = JSON.parse(storedUser);
            }

            // Try to get updated profile from server
            if (Authentication.isAuthenticated()) {
                try {
                    const response = await Authentication.makeAuthenticatedRequest('/api/user/profile');
                    
                    if (response.ok) {
                        const data = await response.json();
                        if (data.success && data.user) {
                            this.profileUser = data.user;
                            localStorage.setItem('currentUser', JSON.stringify(this.profileUser));
                        }
                    }
                } catch (error) {
                    console.warn('Could not fetch updated profile from server:', error);
                }
            }

            if (this.profileUser) {
                this.displayProfile();
            } else {
                this.showProfileError('Profile not found');
            }
        } catch (error) {
            console.error('Error loading profile:', error);
            this.showProfileError('Failed to load profile');
        } finally {
            this.hideProfileLoading();
        }
    },

    // Display profile information
    displayProfile() {
        if (!this.profileUser) return;

        // Update profile display elements
        this.updateElement('profileUsername', this.profileUser.username);
        this.updateElement('profileEmail', this.profileUser.email);
        this.updateElement('profileBio', this.profileUser.bio || 'No bio available');
        this.updateElement('profileFollowers', this.profileUser.followers || 0);
        this.updateElement('profileFollowing', this.profileUser.following || 0);
        this.updateElement('profileStories', this.profileUser.stories || 0);
        this.updateElement('profileJoinDate', this.formatJoinDate(this.profileUser.createdAt));

        // Update avatar
        const avatar = document.getElementById('profileAvatar');
        if (avatar) {
            avatar.textContent = this.profileUser.username ? this.profileUser.username[0].toUpperCase() : 'U';
        }

        // Update editable fields
        this.updateInputElement('editUsername', this.profileUser.username);
        this.updateInputElement('editBio', this.profileUser.bio || '');
    },

    // Update element content
    updateElement(elementId, content) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = content;
        }
    },

    // Update input element value
    updateInputElement(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            element.value = value || '';
        }
    },

    // Format join date
    formatJoinDate(dateString) {
        if (!dateString) return 'Unknown';
        
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            });
        } catch (error) {
            return 'Unknown';
        }
    },

    // Toggle edit mode
    toggleEditMode() {
        this.isEditing = !this.isEditing;
        
        const profileView = document.getElementById('profileView');
        const profileEdit = document.getElementById('profileEdit');
        
        if (profileView && profileEdit) {
            if (this.isEditing) {
                profileView.style.display = 'none';
                profileEdit.style.display = 'block';
                this.populateEditForm();
            } else {
                profileView.style.display = 'block';
                profileEdit.style.display = 'none';
            }
        }
    },

    // Populate edit form
    populateEditForm() {
        if (!this.profileUser) return;
        
        this.updateInputElement('editUsername', this.profileUser.username);
        this.updateInputElement('editBio', this.profileUser.bio);
    },

    // Save profile changes
    async saveProfile() {
        try {
            if (!Authentication.isAuthenticated()) {
                this.showProfileError('You must be logged in to edit your profile');
                return;
            }

            const username = document.getElementById('editUsername')?.value?.trim();
            const bio = document.getElementById('editBio')?.value?.trim();

            if (!username) {
                this.showProfileError('Username is required');
                return;
            }

            const updateData = { username, bio };
            const userId = this.profileUser.uid || this.profileUser.id;

            this.showProfileSaving();

            const response = await Authentication.makeAuthenticatedRequest(`/api/users/${userId}`, {
                method: 'PUT',
                body: JSON.stringify(updateData)
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    // Update local profile data
                    this.profileUser = { ...this.profileUser, ...updateData };
                    localStorage.setItem('currentUser', JSON.stringify(this.profileUser));
                    
                    // Update AppState
                    AppState.currentUser = this.profileUser;
                    
                    // Refresh display
                    this.displayProfile();
                    this.toggleEditMode();
                    
                    this.showProfileSuccess('Profile updated successfully');
                } else {
                    throw new Error(result.error || 'Failed to update profile');
                }
            } else {
                throw new Error('Failed to update profile');
            }
        } catch (error) {
            console.error('Error saving profile:', error);
            this.showProfileError('Failed to save profile changes');
        } finally {
            this.hideProfileSaving();
        }
    },

    // Cancel edit
    cancelEdit() {
        this.toggleEditMode();
        this.populateEditForm(); // Reset form to original values
    },

    // Handle avatar change
    handleAvatarChange(event) {
        const file = event.target.files[0];
        if (file) {
            // For now, just show an alert. In the future, implement file upload
            alert('Avatar upload feature coming soon!');
            console.log('Selected file:', file.name);
        }
    },

    // Show profile loading
    showProfileLoading() {
        const loading = document.getElementById('profileLoading');
        if (loading) {
            loading.style.display = 'block';
        }
    },

    // Hide profile loading
    hideProfileLoading() {
        const loading = document.getElementById('profileLoading');
        if (loading) {
            loading.style.display = 'none';
        }
    },

    // Show profile saving
    showProfileSaving() {
        const saveBtn = document.getElementById('saveProfileBtn');
        if (saveBtn) {
            saveBtn.disabled = true;
            saveBtn.textContent = 'Saving...';
        }
    },

    // Hide profile saving
    hideProfileSaving() {
        const saveBtn = document.getElementById('saveProfileBtn');
        if (saveBtn) {
            saveBtn.disabled = false;
            saveBtn.textContent = 'Save Changes';
        }
    },

    // Show profile error
    showProfileError(message) {
        CoreUtils.showError(message);
    },

    // Show profile success
    showProfileSuccess(message) {
        // TODO: Implement proper success notification
        console.log('Success:', message);
        alert(message); // Temporary
    }
};

// Initialize profile when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Wait for other modules to initialize
    setTimeout(() => {
        Profile.init();
    }, 300);
});

// Make profile available globally
window.Profile = Profile;
